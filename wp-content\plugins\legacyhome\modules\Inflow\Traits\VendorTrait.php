<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait VendorTrait
{
	protected function getVendorResource(): string
	{
		return 'vendors';
	}

	/**
	 * @param array $options e.g. ['filter' => [...], 'include' => '...', 'count' => 50, 'sort' => 'name']
	 * @return array|WP_Error
	 */
	public function listVendors( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getVendorResource(), null, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function getVendor( string|int $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getVendorResource(), $id, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function createVendor( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getVendorResource(), null, [], $data );
	}

	/**
	 * @return array|WP_Error
	 */
	public function updateVendor( string|int $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getVendorResource(), $id, [], $data );
	}

	/**
	 * @return bool|WP_Error
	 */
	public function deleteVendor( string|int $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getVendorResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
