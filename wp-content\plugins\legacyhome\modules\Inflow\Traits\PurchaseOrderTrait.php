<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait PurchaseOrderTrait
{
	protected function getPurchaseOrderResource(): string
	{
		return 'purchase-orders';
	}

	/**
	 * @param array $options e.g. ['filter' => [...], 'include' => '...', 'count' => 50, 'sort' => 'orderNumber']
	 * @return array|WP_Error
	 */
	public function listPurchaseOrders( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getPurchaseOrderResource(), null, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function getPurchaseOrder( string|int $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getPurchaseOrderResource(), $id, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function createPurchaseOrder( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getPurchaseOrderResource(), null, [], $data );
	}

	/**
	 * @return array|WP_Error
	 */
	public function updatePurchaseOrder( string|int $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getPurchaseOrderResource(), $id, [], $data );
	}

	/**
	 * @return bool|WP_Error
	 */
	public function deletePurchaseOrder( string|int $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getPurchaseOrderResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
