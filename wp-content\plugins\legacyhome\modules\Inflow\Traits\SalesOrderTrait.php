<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait SalesOrderTrait
{
	protected function getSalesOrderResource(): string
	{
		return 'sales-orders';
	}

	/**
	 * @param array $options e.g. ['filter' => [...], 'include' => '...', 'count' => 50, 'sort' => 'orderNumber']
	 * @return array|WP_Error
	 */
	public function listSalesOrders( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getSalesOrderResource(), null, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function getSalesOrder( string|int $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getSalesOrderResource(), $id, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function createSalesOrder( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getSalesOrderResource(), null, [], $data );
	}

	/**
	 * @return array|WP_Error
	 */
	public function updateSalesOrder( string|int $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getSalesOrderResource(), $id, [], $data );
	}

	/**
	 * @return bool|WP_Error
	 */
	public function deleteSalesOrder( string|int $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getSalesOrderResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
