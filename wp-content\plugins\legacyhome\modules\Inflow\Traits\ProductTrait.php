<?php

declare( strict_types=1 );

namespace Contactjavas\Legacyhome\Module\Inflow\Traits;

use WP_Error;

/**
 * @psalm-require-implements Contactjavas\Legacyhome\Module\Inflow\Interfaces\InflowRequestContract
 */
trait ProductTrait
{
	protected function getProductResource(): string
	{
		return 'products';
	}

	/**
	 * @param array $options e.g. ['filter' => [...], 'include' => '...', 'count' => 50, 'sort' => 'name']
	 * @return array|WP_Error
	 */
	public function listProducts( array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getProductResource(), null, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function getProduct( string|int $id, array $options = [] ): array|WP_Error
	{
		return $this->request( 'GET', $this->getProductResource(), $id, $options );
	}

	/**
	 * @return array|WP_Error
	 */
	public function createProduct( array $data ): array|WP_Error
	{
		return $this->request( 'POST', $this->getProductResource(), null, [], $data );
	}

	/**
	 * @return array|WP_Error
	 */
	public function updateProduct( string|int $id, array $data ): array|WP_Error
	{
		return $this->request( 'PUT', $this->getProductResource(), $id, [], $data );
	}

	/**
	 * @return bool|WP_Error
	 */
	public function deleteProduct( string|int $id ): bool|WP_Error
	{
		$res = $this->request( 'DELETE', $this->getProductResource(), $id );
		return is_wp_error( $res ) ? $res : true;
	}
}
